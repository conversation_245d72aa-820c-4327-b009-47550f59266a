<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SUZ Accessibility Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0f172a;
            color: white;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .pass { background: rgba(34, 197, 94, 0.2); color: #4ade80; }
        .fail { background: rgba(239, 68, 68, 0.2); color: #f87171; }
        .warning { background: rgba(245, 158, 11, 0.2); color: #fbbf24; }
        h1, h2 { color: #60a5fa; }
        code { 
            background: rgba(100, 116, 139, 0.2); 
            padding: 2px 6px; 
            border-radius: 4px; 
            font-family: 'Courier New', monospace;
        }
        button {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: linear-gradient(135deg, #2563eb, #1e40af);
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SUZ Service Images - Accessibility & Performance Test</h1>
        
        <div class="test-section">
            <h2>Automated Tests</h2>
            <button onclick="runAccessibilityTests()">Run Accessibility Tests</button>
            <button onclick="runPerformanceTests()">Run Performance Tests</button>
            <button onclick="runResponsiveTests()">Run Responsive Tests</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        function addResult(title, status, message) {
            const results = document.getElementById('results');
            const section = document.createElement('div');
            section.className = 'test-section';
            
            const heading = document.createElement('h2');
            heading.textContent = title;
            
            const result = document.createElement('div');
            result.className = `test-result ${status}`;
            result.innerHTML = message;
            
            section.appendChild(heading);
            section.appendChild(result);
            results.appendChild(section);
        }

        async function runAccessibilityTests() {
            document.getElementById('results').innerHTML = '';
            
            // Test 1: Check if service images have proper alt text
            try {
                const response = await fetch('/');
                const html = await response.text();
                
                const altTextRegex = /alt="[^"]*Reinigungsdienstleistung[^"]*"/g;
                const matches = html.match(altTextRegex);
                
                if (matches && matches.length >= 6) {
                    addResult('Alt Text Test', 'pass', 
                        `✓ Found ${matches.length} service images with proper German alt text<br>` +
                        `<code>${matches[0]}</code>`);
                } else {
                    addResult('Alt Text Test', 'fail', 
                        '✗ Service images missing proper German alt text');
                }
            } catch (error) {
                addResult('Alt Text Test', 'fail', `✗ Error testing alt text: ${error.message}`);
            }

            // Test 2: Check lazy loading implementation
            try {
                const response = await fetch('/');
                const html = await response.text();
                
                if (html.includes('loading="lazy"') && html.includes('loading="eager"')) {
                    addResult('Lazy Loading Test', 'pass', 
                        '✓ Proper lazy loading implementation found (eager for above-fold, lazy for below-fold)');
                } else if (html.includes('loading="lazy"')) {
                    addResult('Lazy Loading Test', 'warning', 
                        '⚠ Lazy loading found but may need optimization for above-fold images');
                } else {
                    addResult('Lazy Loading Test', 'fail', 
                        '✗ No lazy loading implementation found');
                }
            } catch (error) {
                addResult('Lazy Loading Test', 'fail', `✗ Error testing lazy loading: ${error.message}`);
            }

            // Test 3: Check responsive image attributes
            try {
                const response = await fetch('/');
                const html = await response.text();
                
                if (html.includes('sizes=') && html.includes('width=') && html.includes('height=')) {
                    addResult('Responsive Images Test', 'pass', 
                        '✓ Responsive image attributes found (sizes, width, height)');
                } else {
                    addResult('Responsive Images Test', 'warning', 
                        '⚠ Some responsive image attributes may be missing');
                }
            } catch (error) {
                addResult('Responsive Images Test', 'fail', `✗ Error testing responsive images: ${error.message}`);
            }
        }

        async function runPerformanceTests() {
            // Test image loading performance
            const services = [
                'Hotelzimmerreinigung-min.png',
                'Teppichreinigung-min.png', 
                'Bodenreinigung-min.png',
                'Gemeinschaftsräume-min.png',
                'Büroreinigung-min.png',
                'Krankenhausreinigung-min.png'
            ];

            let totalLoadTime = 0;
            let successCount = 0;

            for (const service of services) {
                try {
                    const startTime = performance.now();
                    const response = await fetch(`/assets/images/services/${service}`);
                    const endTime = performance.now();
                    
                    if (response.ok) {
                        totalLoadTime += (endTime - startTime);
                        successCount++;
                    }
                } catch (error) {
                    console.error(`Failed to load ${service}:`, error);
                }
            }

            const avgLoadTime = totalLoadTime / successCount;
            
            if (successCount === services.length && avgLoadTime < 100) {
                addResult('Image Loading Performance', 'pass', 
                    `✓ All ${successCount} images loaded successfully<br>` +
                    `Average load time: ${avgLoadTime.toFixed(2)}ms`);
            } else if (successCount === services.length) {
                addResult('Image Loading Performance', 'warning', 
                    `⚠ All images loaded but average time is ${avgLoadTime.toFixed(2)}ms (consider optimization)`);
            } else {
                addResult('Image Loading Performance', 'fail', 
                    `✗ Only ${successCount}/${services.length} images loaded successfully`);
            }
        }

        async function runResponsiveTests() {
            // Test CSS media queries and responsive design
            const mediaQueries = [
                '(max-width: 768px)',
                '(max-width: 1024px)',
                '(min-width: 1025px)'
            ];

            let responsiveSupport = 0;

            mediaQueries.forEach(query => {
                if (window.matchMedia(query)) {
                    responsiveSupport++;
                }
            });

            if (responsiveSupport === mediaQueries.length) {
                addResult('Responsive Design Test', 'pass', 
                    '✓ All responsive breakpoints supported');
            } else {
                addResult('Responsive Design Test', 'warning', 
                    `⚠ ${responsiveSupport}/${mediaQueries.length} responsive breakpoints supported`);
            }

            // Test viewport meta tag
            const viewportMeta = document.querySelector('meta[name="viewport"]');
            if (viewportMeta && viewportMeta.content.includes('width=device-width')) {
                addResult('Viewport Configuration', 'pass', 
                    '✓ Proper viewport meta tag found');
            } else {
                addResult('Viewport Configuration', 'fail', 
                    '✗ Missing or incorrect viewport meta tag');
            }
        }
    </script>
</body>
</html>
